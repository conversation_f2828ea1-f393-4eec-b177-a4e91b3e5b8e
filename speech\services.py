"""
Speech Services - Speech-to-Text and Text-to-Speech Integration

Provides abstracted interfaces for speech processing services.
Currently implements AssemblyAI, designed for easy provider swapping.
"""

import os
import logging
import tempfile
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from django.conf import settings
import assemblyai as aai
import requests

logger = logging.getLogger(__name__)


class SpeechToTextProvider(ABC):
    """Abstract base class for Speech-to-Text providers"""
    
    @abstractmethod
    def transcribe_audio(self, audio_file_path: str) -> Dict[str, Any]:
        """Transcribe audio file to text"""
        pass
    
    @abstractmethod
    def is_configured(self) -> bool:
        """Check if the provider is properly configured"""
        pass


class TextToSpeechProvider(ABC):
    """Abstract base class for Text-to-Speech providers"""
    
    @abstractmethod
    def synthesize_speech(self, text: str, voice: str = None) -> Dict[str, Any]:
        """Convert text to speech"""
        pass
    
    @abstractmethod
    def is_configured(self) -> bool:
        """Check if the provider is properly configured"""
        pass


class AssemblyAISpeechToText(SpeechToTextProvider):
    """AssemblyAI Speech-to-Text implementation"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'ASSEMBLYAI_API_KEY', None)
        self._configure()
    
    def _configure(self):
        """Configure AssemblyAI"""
        if self.api_key:
            try:
                aai.settings.api_key = self.api_key
                logger.info("AssemblyAI configured successfully")
            except Exception as e:
                logger.error(f"Failed to configure AssemblyAI: {e}")
        else:
            logger.warning("AssemblyAI API key not found in settings")
    
    def is_configured(self) -> bool:
        """Check if AssemblyAI is properly configured"""
        return self.api_key is not None
    
    def transcribe_audio(self, audio_file_path: str) -> Dict[str, Any]:
        """Transcribe audio using AssemblyAI"""
        if not self.is_configured():
            return {
                'success': False,
                'error': 'AssemblyAI not configured',
                'text': ''
            }
        
        try:
            # Create transcriber
            transcriber = aai.Transcriber()
            
            # Transcribe the audio file
            transcript = transcriber.transcribe(audio_file_path)
            
            if transcript.status == aai.TranscriptStatus.error:
                return {
                    'success': False,
                    'error': transcript.error,
                    'text': ''
                }
            
            return {
                'success': True,
                'text': transcript.text,
                'confidence': getattr(transcript, 'confidence', None),
                'provider': 'assemblyai'
            }
            
        except Exception as e:
            logger.error(f"AssemblyAI transcription error: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': ''
            }


class WebSpeechAPITextToSpeech(TextToSpeechProvider):
    """Web Speech API Text-to-Speech (client-side implementation)"""
    
    def __init__(self):
        # This is a placeholder for client-side TTS
        # The actual implementation will be in JavaScript
        pass
    
    def is_configured(self) -> bool:
        """Web Speech API is available in most modern browsers"""
        return True
    
    def synthesize_speech(self, text: str, voice: str = None) -> Dict[str, Any]:
        """Return configuration for client-side TTS"""
        return {
            'success': True,
            'text': text,
            'voice': voice or 'default',
            'provider': 'web_speech_api',
            'client_side': True,
            'instructions': 'Use Web Speech API on client side'
        }


class ElevenLabsTextToSpeech(TextToSpeechProvider):
    """ElevenLabs Text-to-Speech implementation (placeholder)"""
    
    def __init__(self):
        self.api_key = os.getenv('ELEVENLABS_API_KEY')
        self.voice_id = "21m00Tcm4TlvDq8ikWAM"  # Default voice
    
    def is_configured(self) -> bool:
        """Check if ElevenLabs is configured"""
        return self.api_key is not None
    
    def synthesize_speech(self, text: str, voice: str = None) -> Dict[str, Any]:
        """Synthesize speech using ElevenLabs API"""
        if not self.is_configured():
            return {
                'success': False,
                'error': 'ElevenLabs API not configured',
                'audio_url': None
            }
        
        # This is a placeholder implementation
        # In a real implementation, you would call the ElevenLabs API
        return {
            'success': False,
            'error': 'ElevenLabs integration not yet implemented',
            'audio_url': None
        }


class SpeechService:
    """Main speech service that manages providers"""
    
    def __init__(self):
        self.stt_providers = {
            'assemblyai': AssemblyAISpeechToText(),
            # Future STT providers can be added here
        }
        
        self.tts_providers = {
            'web_speech_api': WebSpeechAPITextToSpeech(),
            'elevenlabs': ElevenLabsTextToSpeech(),
            # Future TTS providers can be added here
        }
        
        self.default_stt_provider = 'assemblyai'
        self.default_tts_provider = 'web_speech_api'
    
    def get_stt_provider(self, provider_name: str = None) -> SpeechToTextProvider:
        """Get a Speech-to-Text provider"""
        if provider_name is None:
            provider_name = self.default_stt_provider
        
        provider = self.stt_providers.get(provider_name)
        if provider is None:
            raise ValueError(f"STT Provider '{provider_name}' not found")
        
        return provider
    
    def get_tts_provider(self, provider_name: str = None) -> TextToSpeechProvider:
        """Get a Text-to-Speech provider"""
        if provider_name is None:
            provider_name = self.default_tts_provider
        
        provider = self.tts_providers.get(provider_name)
        if provider is None:
            raise ValueError(f"TTS Provider '{provider_name}' not found")
        
        return provider
    
    def transcribe_audio(self, audio_file_path: str, provider: str = None) -> Dict[str, Any]:
        """Transcribe audio using specified or default provider"""
        try:
            stt_provider = self.get_stt_provider(provider)
            return stt_provider.transcribe_audio(audio_file_path)
        except Exception as e:
            logger.error(f"Speech transcription error: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': ''
            }
    
    def synthesize_speech(self, text: str, voice: str = None, provider: str = None) -> Dict[str, Any]:
        """Synthesize speech using specified or default provider"""
        try:
            tts_provider = self.get_tts_provider(provider)
            return tts_provider.synthesize_speech(text, voice)
        except Exception as e:
            logger.error(f"Speech synthesis error: {e}")
            return {
                'success': False,
                'error': str(e),
                'audio_url': None
            }
    
    def save_uploaded_audio(self, audio_file) -> str:
        """Save uploaded audio file to temporary location"""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
                for chunk in audio_file.chunks():
                    temp_file.write(chunk)
                return temp_file.name
        except Exception as e:
            logger.error(f"Error saving audio file: {e}")
            raise
    
    def cleanup_temp_file(self, file_path: str):
        """Clean up temporary audio file"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            logger.error(f"Error cleaning up temp file {file_path}: {e}")
    
    def get_provider_status(self) -> Dict[str, Dict[str, bool]]:
        """Get configuration status of all providers"""
        return {
            'speech_to_text': {
                name: provider.is_configured() 
                for name, provider in self.stt_providers.items()
            },
            'text_to_speech': {
                name: provider.is_configured() 
                for name, provider in self.tts_providers.items()
            }
        }


# Global service instance
speech_service = SpeechService()
