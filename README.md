# 🌟 AI-Powered Wisdom - Modular Multi-Domain Guidance Platform

> **🚀 Live Application**: https://mcpdivine.vercel.app/

A sophisticated, highly modular AI application that provides personalized guidance through multiple wisdom domains including Biblical Texts, Buddhist Teachings, and Self-Help Philosophy. Features both chat and speech interfaces with seamless domain switching and context-aware responses.

## 🌐 **Live Deployment**

- **Frontend**: https://mcpdivine.vercel.app/
- **Backend API**: https://ai-powered-wisdom-backend.onrender.com
- **Health Check**: https://ai-powered-wisdom-backend.onrender.com/api/chat/health/
- **API Documentation**: Available at backend URL + `/api/`

###  **Quick Start**

1. **Visit the live app**: https://mcpdivine.vercel.app/
2. **Choose a wisdom domain** in Settings (Biblical, Buddhist, Self-Help)
3. **Start chatting** or use voice interaction

## **Architecture Overview**

### **Core Design Principles**

This application demonstrates **extreme modularity** as requested in the technical requirements:

- **🔄 Content as a Black Box**: All content is stored in swappable modules (`content/domains/`)

  - Switch from Biblical → Buddhist → Self-Help → Any domain without code changes
  - JSON-based content loading system
  - Database abstraction for content management
- **🤖 LLM as a Black Box**: Clean abstraction for easy provider switching (`llm_integration/`)

  - Current: Google Gemini API implementation
  - Future: OpenAI, Anthropic, or any LLM provider
  - Configurable API calls and response handling
- **🔌 Plugin Architecture**: New content domains can be added without code changes

  - Modular Django apps for different functionalities
  - Clean interfaces between components
  - Extensible design for future enhancements
- **🚀 Future-Ready**: Designed for RAG, document retrieval, and external context injection

  - Placeholder functions for context fetching
  - Scalable architecture for specialized content
  - Ready for vector databases and semantic search

### Modular Architecture Explanation

#### Content Layer (Black Box Design)

```
content/domains/[domain_name]/sample_content.json
```

- **Purpose**: Completely isolated content storage
- **Swappable**: Replace any domain without touching code
- **Structure**: JSON-based with domain config, categories, and content pieces
- **Loading**: `python manage.py load_sample_content --domain [name] --file [path]`

#### LLM Layer (Provider Abstraction)

```
llm_integration/services.py
├── LLMProvider (Abstract Base Class)
├── GeminiProvider (Current Implementation)
└── [Future: OpenAIProvider, AnthropicProvider, etc.]
```

- **Purpose**: Clean abstraction over different AI providers
- **Extensible**: Add new providers by implementing LLMProvider interface
- **Configurable**: Environment-based configuration for API keys and settings

#### Content Management Layer

```
content_management/
├── models.py (ContentDomain, ContentCategory, ContentPiece)
├── services.py (ContentService for retrieval and search)
└── management/commands/ (Content loading utilities)
```

- **Purpose**: Database-driven content management with search capabilities
- **Dynamic**: Real-time domain switching without application restart
- **Scalable**: Ready for vector databases and semantic search

#### Application Layer

```
chat/ (Text Interface) + speech/ (Voice Interface)
├── views.py (API endpoints)
├── serializers.py (Request/response validation)
└── services.py (Business logic)
```

- **Purpose**: User interface and API endpoints
- **Modular**: Separate apps for different interaction modes
- **Consistent**: Shared patterns across chat and speech interfaces

### **💻 Technology Stack**

**Backend Architecture:**

- **Django 5.2.4**: Web framework with modular app structure
- **Django REST Framework**: RESTful API development
- **Google Gemini API**: LLM integration (easily swappable)
- **AssemblyAI**: Speech-to-text processing
- **SQLite/PostgreSQL**: Database with migration support

**Frontend Architecture:**

- **React 19**: Modern UI framework with hooks
- **Vite**: Lightning-fast build tool and dev server
- **Axios**: HTTP client for API communication
- **Lucide React**: Beautiful icon library
- **React Markdown**: Rich text rendering for AI responses

**Deployment Infrastructure:**

- **Vercel**: Frontend hosting with global CDN
- **Render**: Backend hosting with auto-scaling
- **GitHub**: Version control with CI/CD integration

## ✨ **Features & Functionality**

### **💬 Chat Interface**

- **Multiple Interaction Types**: Daily Guidance, Interpretation, Conversational, Therapeutic
- **Context-Aware Responses**: Uses domain-specific content + LLM world knowledge
- **Session Management**: Persistent chat history and user sessions
- **Real-Time Communication**: Instant responses with loading indicators
- **Markdown Support**: Rich text formatting for AI responses

### **🎤 Speech Interface**

- **Speech-to-Text**: High-quality voice recognition using AssemblyAI
- **Text-to-Speech**: Natural voice synthesis using Web Speech API
- **Complete Voice Conversations**: Hands-free interaction capability
- **Voice Controls**: Start/stop recording, playback controls
- **Accessibility**: Full voice-driven experience for users with disabilities

### **Content Management (Black Box Design)**

- **Three Active Domains**: Biblical Texts, Buddhist Teachings, Self-Help Philosophy
- **Real-Time Domain Switching**: Seamless switching without application restart
- **Domain-Specific Instructions**: Each domain has tailored AI behavior and responses
- **Rich Content Database**: Categorized content with search and retrieval capabilities
- **JSON-Based Loading**: `python manage.py load_sample_content --domain [name] --file [path]`
- **Swappable Content**: Replace any domain without touching application code

### **LLM Integration (Provider Abstraction)**

- **Abstract Provider Interface**: Clean abstraction over different AI providers
- **Current Implementation**: Google Gemini API with full feature support
- **Easy Provider Switching**: Add new providers by implementing `LLMProvider` interface
- **Configurable Contexts**: Different interaction types and response styles
- **Error Handling**: Graceful fallbacks and retry mechanisms

## 🎯 **Meeting Technical Requirements**

### **✅ Functional Requirements Fulfilled**

#### **Chat Interface** ✅

- Users can type questions and receive AI-powered responses
- Multiple interaction types: Daily guidance, interpretations, conversational support
- Context-aware responses using domain-specific content

#### **Speech Interface** ✅

- Users can speak questions and receive audio responses
- Complete voice-driven interaction capability
- AssemblyAI integration for speech-to-text processing

#### **Knowledge Domain** ✅

- Works with specific content corpus (Biblical, Buddhist, Self-Help)
- Sample content provided in `content/domains/` structure
- Easy domain switching and content management

#### **Response Types** ✅

- Daily guidance and quotes
- Interpretation requests
- General conversational support
- Therapeutic-style dialogue

### **✅ Technical Requirements Fulfilled**

#### **Backend: Django** ✅

- Django 5.2.4 with Django REST Framework
- Modular app structure (chat, speech, content_management, llm_integration)
- Clean API design with proper serialization

#### **Frontend: React** ✅

- React 19 with modern hooks and components
- Vite for fast development and building
- Responsive design with mobile support

#### **LLM Integration** ✅

- Google Gemini API for production-quality responses
- Actual LLM API integration (not mock responses)
- Proper error handling and retry mechanisms

#### **Speech APIs** ✅

- AssemblyAI for speech-to-text (free tier compatible)
- Web Speech API for text-to-speech
- Complete voice interaction pipeline

#### **Deployment** ✅

- **Live Application**: https://mcpdivine.vercel.app/
- **Backend API**: https://ai-powered-wisdom-backend.onrender.com
- Clear local setup instructions provided

### **✅ Modularity Requirements Fulfilled**

#### **Content as Black Box** ✅

- All content stored in `content/domains/` directory
- Easily swappable: Bible → Buddhist → Self-Help → Any domain
- JSON-based content structure with database abstraction
- No code changes required for new domains

#### **LLM as Black Box** ✅

- Clean `LLMProvider` abstract base class in `llm_integration/services.py`
- Current `GeminiProvider` implementation
- Easy to add OpenAI, Anthropic, or custom providers
- Configurable API calls and response handling

#### **Plugin Architecture** ✅

- New content domains can be added without code changes
- Modular Django apps for different functionalities
- Clean interfaces between components
- Future-ready for RAG and document retrieval

## **Project Structure & File Purpose**

### **Clean Production Codebase Structure**

```
mcp2/                                # Project Root
├── 📁 llm_wrapper_backend/          # Django Project Configuration
│   ├── __init__.py                  # Python package marker
│   ├── settings.py                  # Django settings, database, CORS, API keys
│   ├── urls.py                      # Main URL routing, API endpoint definitions
│   ├── cors_middleware.py           # Custom CORS handling middleware
│   ├── wsgi.py                      # WSGI configuration for deployment
│   ├── asgi.py                      # ASGI configuration for async support
│   └── README.md                    # Backend-specific documentation
│
├── 📁 chat/                         # Chat Interface Django App (Black Box)
│   ├── __init__.py                  # Python package marker
│   ├── models.py                    # Database models for chat history, interactions
│   ├── views.py                     # API endpoints for chat, daily guidance, domains
│   ├── serializers.py               # Request/response data validation and formatting
│   ├── urls.py                      # Chat app URL routing
│   ├── apps.py                      # Django app configuration
│   ├── admin.py                     # Django admin interface configuration
│   ├── tests.py                     # Unit tests for chat functionality
│   └── migrations/                  # Database migration files
│
├── 📁 speech/                       # Speech Processing Django App (Black Box)
│   ├── __init__.py                  # Python package marker
│   ├── models.py                    # Database models for speech interactions
│   ├── views.py                     # API endpoints for STT, TTS, speech chat
│   ├── serializers.py               # Speech request/response validation
│   ├── services.py                  # Speech processing logic (AssemblyAI, Web Speech)
│   ├── urls.py                      # Speech app URL routing
│   ├── apps.py                      # Django app configuration
│   ├── admin.py                     # Django admin interface configuration
│   ├── tests.py                     # Unit tests for speech functionality
│   └── migrations/                  # Database migration files
│
├── 📁 content_management/           # Content Domain Management (Black Box)
│   ├── __init__.py                  # Python package marker
│   ├── models.py                    # ContentDomain, ContentCategory, ContentPiece models
│   ├── services.py                  # Content retrieval, search, domain switching logic
│   ├── admin.py                     # Django admin for content management
│   ├── apps.py                      # Django app configuration
│   ├── management/                  # Custom Django management commands
│   │   └── commands/
│   │       └── load_sample_content.py  # Command to load content from JSON files
│   ├── tests.py                     # Unit tests for content management
│   └── migrations/                  # Database migration files
│
├── 📁 llm_integration/              # LLM Provider Abstraction (Black Box)
│   ├── __init__.py                  # Python package marker
│   ├── services.py                  # LLMProvider base class, GeminiProvider implementation
│   ├── models.py                    # LLM interaction logging models
│   ├── apps.py                      # Django app configuration
│   ├── admin.py                     # Django admin interface configuration
│   ├── tests.py                     # Unit tests for LLM integration
│   └── migrations/                  # Database migration files
│
├── 📁 frontend/                     # React Frontend Application (Black Box)
│   ├── 📁 public/                   # Static assets served by Vite
│   │   ├── index.html               # Main HTML template
│   │   └── vite.svg                 # Vite logo
│   ├── 📁 src/                      # React source code
│   │   ├── 📁 components/           # Modular React components
│   │   │   ├── ChatInterface.jsx    # Text-based chat interface
│   │   │   ├── ChatInterface.css    # Chat interface styles
│   │   │   ├── SpeechInterface.jsx  # Voice-based chat interface
│   │   │   ├── SpeechInterface.css  # Speech interface styles
│   │   │   ├── DomainSelector.jsx   # Domain switching interface
│   │   │   ├── DomainSelector.css   # Domain selector styles
│   │   │   ├── Header.jsx           # Navigation header component
│   │   │   └── Header.css           # Header component styles
│   │   ├── App.jsx                  # Root component with routing and global state
│   │   ├── App.css                  # Global application styles
│   │   ├── config.js                # Environment-based configuration
│   │   ├── main.jsx                 # React application entry point
│   │   └── index.css                # Global CSS styles
│   ├── 📁 node_modules/             # Installed npm packages (auto-generated)
│   ├── package.json                 # Node.js dependencies and scripts
│   ├── package-lock.json            # Locked dependency versions
│   ├── vite.config.js               # Vite build configuration
│   ├── eslint.config.js             # ESLint configuration for code quality
│   └── README.md                    # Frontend-specific documentation
│
├── 📁 content/                      # Content Domains (Black Box Design)
│   └── 📁 domains/                  # Swappable content domains
│       ├── 📁 biblical_texts/       # Biblical Wisdom Domain
│       │   └── sample_content.json  # Biblical verses, teachings, spiritual guidance
│       ├── 📁 buddhist_teachings/   # Buddhist Philosophy Domain
│       │   └── sample_content.json  # Meditation practices, Four Noble Truths, mindfulness
│       └── 📁 self_help/           # Personal Development Domain
│           └── sample_content.json  # Goal-setting, productivity, motivation strategies
│
├── 📁 static/                       # Django Static Files (auto-generated)
├── 📁 venv/                         # Python Virtual Environment (auto-generated)
│
├── 📄 **Core Configuration Files**
├── manage.py                        # Django management script
├── requirements.txt                 # Python dependencies
├── db.sqlite3                       # SQLite database with loaded content
├── .env                             # Environment variables (API keys, settings)
│
└── 📄 **Documentation**
    ├── README.md                    # Main project documentation and setup guide
    └── DOCUMENTATION.md             # Comprehensive technical documentation
```

### **Key Architecture Highlights**

#### **Modular Django Apps (Black Box Design)**

- **`chat/`**: Complete text-based interaction system
- **`speech/`**: Full voice interaction pipeline
- **`content_management/`**: Swappable content domain system
- **`llm_integration/`**: Abstract LLM provider interface

#### **Content as Black Box**

- **`content/domains/`**: Easily swappable wisdom domains
- **JSON Configuration**: Add new domains without code changes
- **Database Loading**: `python manage.py load_sample_content --domain [name] --file [path]`

#### **Frontend Components (Black Box Design)**

- **`ChatInterface.jsx`**: Text-based conversation component
- **`SpeechInterface.jsx`**: Voice interaction component
- **`DomainSelector.jsx`**: Domain switching component
- **`config.js`**: Environment-based API configuration

#### **Clean Production Structure**

- **No Development Files**: Removed testing scripts, deployment configs
- **No Build Artifacts**: Excluded cache files, temporary files
- **Core Functionality Only**: Focus on essential application components
- **Professional Presentation**: Clean, recruiter-ready codebase

## 🛠️ Installation & Setup

### Prerequisites

- Python 3.8+
- Node.js 16+
- Git

### Backend Setup

1. **Clone and navigate to the project**

```bash
cd llm_wrapper
```

2. **Create virtual environment**

```bash
python -m venv venv
# Windows
.\venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. **Install Python dependencies**

```bash
pip install django djangorestframework django-cors-headers python-dotenv google-generativeai assemblyai requests
```

4. **Configure environment variables**
   Create a `.env` file in the root directory:

```env
# LLM API Keys
GEMINI_API_KEY=your_gemini_api_key_here

# Speech API Keys
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here

# Django Settings
SECRET_KEY=your_secret_key_here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173

# Content Domain Configuration
ACTIVE_CONTENT_DOMAIN=biblical_texts
CONTENT_BASE_PATH=content/domains/
```

5. **Run database migrations**

```bash
python manage.py makemigrations
python manage.py migrate
```

6. **Load sample content for all domains**

```bash
# Load Biblical content
python manage.py load_sample_content --domain biblical_texts --file content/domains/biblical_texts/sample_content.json

# Load Buddhist content
python manage.py load_sample_content --domain buddhist_teachings --file content/domains/buddhist_teachings/sample_content.json

# Load Self-Help content
python manage.py load_sample_content --domain self_help --file content/domains/self_help/sample_content.json
```

7. **Start the CORS proxy (recommended)**

```bash
python cors_proxy.py
```

8. **Start the Django server**

```bash
python manage.py runserver 8080
```

### Frontend Setup

1. **Navigate to frontend directory**

```bash
cd frontend
```

2. **Install Node.js dependencies**

```bash
npm install
```

3. **Start the development server**

```bash
npm run dev
```

The frontend will be available at `http://localhost:5173`

## Available Wisdom Domains

### Biblical Texts

- **Focus**: Biblical wisdom, spiritual guidance, and Christian teachings
- **Content**: Verses, parables, and spiritual insights from the Bible
- **Interaction Style**: Compassionate spiritual guidance with biblical references
- **Use Cases**: Daily devotions, spiritual counseling, biblical interpretation

### Buddhist Teachings

- **Focus**: Mindfulness, meditation, and Buddhist philosophy
- **Content**: Four Noble Truths, Eightfold Path, meditation practices, compassion teachings
- **Interaction Style**: Wise Buddhist teacher emphasizing mindfulness and enlightenment
- **Use Cases**: Meditation guidance, mindfulness practice, philosophical discussions

### Self-Help Philosophy

- **Focus**: Personal development, motivation, and practical life strategies
- **Content**: Goal-setting techniques, productivity methods, positive psychology
- **Interaction Style**: Motivational life coach with actionable advice
- **Use Cases**: Goal achievement, productivity improvement, personal growth

## API Documentation

### Chat Endpoints

#### POST /api/chat/

Send a chat message and receive AI response.

**Request:**

```json
{
  "message": "How can I find peace in difficult times?",
  "interaction_type": "therapeutic",
  "session_id": "optional-session-id",
  "domain_slug": "buddhist_teachings"
}
```

**Response:**

```json
{
  "response": "AI generated response...",
  "interaction_type": "therapeutic",
  "session_id": "session-uuid",
  "success": true,
  "response_time": 1.23,
  "model_info": {
    "provider": "gemini",
    "model": "gemini-1.5-flash"
  }
}
```

#### POST /api/chat/daily-guidance/

Get daily spiritual guidance.

#### GET /api/chat/domains/

List available content domains.

#### POST /api/chat/domains/

Switch active content domain.

### Speech Endpoints

#### POST /api/speech/stt/

Convert speech to text.

#### POST /api/speech/tts/

Convert text to speech.

#### POST /api/speech/chat/

Complete speech-based conversation (STT → LLM → TTS).

## 🔌 Adding New Content Domains

### 1. Create Content Structure

```json
{
  "domain": {
    "name": "Your Domain Name",
    "slug": "your_domain_slug",
    "description": "Domain description",
    "config": {
      "instructions": {
        "daily_guidance": "Instructions for daily guidance...",
        "interpretation": "Instructions for interpretation...",
        "conversational": "Instructions for conversation...",
        "therapeutic": "Instructions for therapeutic responses...",
        "general": "General instructions..."
      }
    }
  },
  "categories": [
    {
      "name": "Category Name",
      "slug": "category_slug",
      "description": "Category description",
      "content": [
        {
          "title": "Content Title",
          "content": "Content text...",
          "reference": "Source reference",
          "type": "text",
          "tags": ["tag1", "tag2"],
          "metadata": {}
        }
      ]
    }
  ]
}
```

### 2. Load Content

```bash
python manage.py load_sample_content --domain your_domain_slug --file path/to/content.json
```

## Adding New LLM Providers

### 1. Create Provider Class

```python
class NewLLMProvider(LLMProvider):
    def __init__(self):
        self.api_key = getattr(settings, 'NEW_LLM_API_KEY', None)
        # Initialize provider
  
    def generate_response(self, prompt: str, context: str = "", **kwargs) -> Dict[str, Any]:
        # Implement response generation
        pass
  
    def is_configured(self) -> bool:
        # Check configuration
        return self.api_key is not None
```

### 2. Register Provider

```python
# In llm_integration/services.py
self.providers = {
    'gemini': GeminiProvider(),
    'new_provider': NewLLMProvider(),
}
```

## 🧪 Testing

### Run Backend Tests

```bash
python manage.py test
```

### Test API Endpoints

```bash
# Test health endpoint
curl http://localhost:8080/api/chat/health/

# Test domains endpoint
curl http://localhost:8080/api/chat/domains/

# Test chat endpoint
curl -X POST http://localhost:8080/api/chat/ \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "interaction_type": "general"}'
```

## Deployment

### Environment Variables for Production

```env
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DATABASE_URL=postgresql://user:password@localhost/dbname
```

### Static Files

```bash
python manage.py collectstatic
```

### Frontend Build

```bash
cd frontend
npm run build
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### **Deployment Architecture**

```
┌─────────────────┐    HTTPS/API     ┌──────────────────┐
│   Vercel CDN    │ ◄──────────────► │   Render Cloud   │
│   (Frontend)    │                  │   (Backend)      │
│                 │                  │                  │
│ • React App     │                  │ • Django API     │
│ • Static Assets │                  │ • PostgreSQL DB  │
│ • Global CDN    │                  │ • Gunicorn       │
└─────────────────┘                  └──────────────────┘
```

### **Environment Configuration**

#### **Frontend Environment Variables (Vercel)**

```env
VITE_API_BASE_URL=https://ai-powered-wisdom-backend.onrender.com/api
VITE_WS_BASE_URL=wss://ai-powered-wisdom-backend.onrender.com
VITE_APP_NAME=AI-Powered Wisdom
VITE_ENABLE_SPEECH=true
```

#### **Backend Environment Variables (Render)**

```env
GEMINI_API_KEY=your_gemini_api_key
ASSEMBLYAI_API_KEY=your_assemblyai_api_key
SECRET_KEY=your_secure_secret_key
DEBUG=False
ALLOWED_HOSTS=*.onrender.com,mcpdivine.vercel.app
CORS_ALLOWED_ORIGINS=https://mcpdivine.vercel.app
```

### **Deployment Process**

#### **Frontend Deployment (Vercel)**

1. **Connected to GitHub**: Auto-deploys on push to main branch
2. **Build Command**: `npm run build`
3. **Output Directory**: `dist`
4. **Framework**: Vite (React)
5. **Root Directory**: `frontend`

#### **Backend Deployment (Render)**

1. **Connected to GitHub**: Auto-deploys on push to main branch
2. **Build Command**: `pip install -r requirements.txt`
3. **Start Command**: `python manage.py migrate && gunicorn llm_wrapper_backend.wsgi:application`
4. **Runtime**: Python 3.11
5. **Database**: Managed PostgreSQL

### **Monitoring & Maintenance**

#### **Health Monitoring**

- **Backend Health**: https://ai-powered-wisdom-backend.onrender.com/api/chat/health/
- **Frontend Status**: Vercel dashboard analytics
- **Database Status**: Render database metrics

#### **Performance Metrics**

- **Frontend**: Global CDN with <100ms response times
- **Backend**: Auto-scaling with 512MB RAM
- **Database**: Managed PostgreSQL with automated backups

## Troubleshooting

### Common Issues

1. **Port already in use**: Change the port in runserver command
2. **API key errors**: Verify your API keys in the .env file
3. **CORS errors**: Check CORS_ALLOWED_ORIGINS in settings
4. **Database errors**: Run migrations with `python manage.py migrate`

### Getting Help

- Check the logs in the Django console
- Verify API endpoints with curl
- Ensure all dependencies are installed
- Check that the virtual environment is activated

## Future Enhancements

- RAG integration for document retrieval
- Multi-language support
- Advanced analytics and usage tracking
- Mobile app development
- Voice cloning and custom TTS
- Integration with external knowledge bases
