"""
Django management command to load sample content into the database.
"""

import json
import os
from django.core.management.base import BaseCommand
from django.conf import settings
from content_management.models import ContentDomain, ContentCategory, ContentPiece


class Command(BaseCommand):
    help = 'Load sample content from JSON files into the database'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--domain',
            type=str,
            default='biblical_texts',
            help='Domain slug to load content for'
        )
        parser.add_argument(
            '--file',
            type=str,
            help='Specific JSON file to load (optional)'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing content before loading'
        )
    
    def handle(self, *args, **options):
        domain_slug = options['domain']
        specific_file = options['file']
        clear_existing = options['clear']
        
        if specific_file:
            file_path = specific_file
        else:
            # Default path
            file_path = os.path.join(
                settings.BASE_DIR,
                'content',
                'domains',
                domain_slug,
                'sample_content.json'
            )
        
        if not os.path.exists(file_path):
            self.stdout.write(
                self.style.ERROR(f'Content file not found: {file_path}')
            )
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Create or get domain
            domain_data = data.get('domain', {})
            domain, created = ContentDomain.objects.get_or_create(
                slug=domain_data.get('slug', domain_slug),
                defaults={
                    'name': domain_data.get('name', domain_slug.replace('_', ' ').title()),
                    'description': domain_data.get('description', ''),
                    'config': domain_data.get('config', {})
                }
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created domain: {domain.name}')
                )
            else:
                self.stdout.write(f'Using existing domain: {domain.name}')
                
                # Update config if provided
                if domain_data.get('config'):
                    domain.config = domain_data['config']
                    domain.save()
                    self.stdout.write('Updated domain configuration')
            
            # Clear existing content if requested
            if clear_existing:
                deleted_count = ContentPiece.objects.filter(
                    category__domain=domain
                ).delete()[0]
                self.stdout.write(f'Deleted {deleted_count} existing content pieces')
                
                deleted_categories = ContentCategory.objects.filter(
                    domain=domain
                ).delete()[0]
                self.stdout.write(f'Deleted {deleted_categories} existing categories')
            
            # Load categories and content
            categories_data = data.get('categories', [])
            total_content_pieces = 0
            
            for category_data in categories_data:
                category, created = ContentCategory.objects.get_or_create(
                    domain=domain,
                    slug=category_data.get('slug', category_data['name'].lower().replace(' ', '_')),
                    defaults={
                        'name': category_data['name'],
                        'description': category_data.get('description', ''),
                        'order': category_data.get('order', 0)
                    }
                )
                
                if created:
                    self.stdout.write(f'  Created category: {category.name}')
                
                # Load content pieces
                content_pieces = category_data.get('content', [])
                for content_data in content_pieces:
                    content_piece, created = ContentPiece.objects.get_or_create(
                        category=category,
                        title=content_data.get('title', 'Untitled'),
                        defaults={
                            'content': content_data.get('content', ''),
                            'content_type': content_data.get('type', 'text'),
                            'reference': content_data.get('reference', ''),
                            'tags': content_data.get('tags', []),
                            'metadata': content_data.get('metadata', {}),
                            'order': content_data.get('order', 0),
                            'is_featured': content_data.get('featured', False)
                        }
                    )
                    
                    if created:
                        total_content_pieces += 1
            
            # Activate the domain
            domain.activate()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully loaded {total_content_pieces} content pieces '
                    f'across {len(categories_data)} categories for domain "{domain.name}"'
                )
            )
            self.stdout.write(
                self.style.SUCCESS(f'Domain "{domain.name}" is now active')
            )
            
        except json.JSONDecodeError as e:
            self.stdout.write(
                self.style.ERROR(f'Invalid JSON in file {file_path}: {e}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error loading content: {e}')
            )
