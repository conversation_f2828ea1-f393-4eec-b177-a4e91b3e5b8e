"""
Content Management Models

Defines the database models for managing different content domains
and their associated content pieces.
"""

from django.db import models
from django.core.validators import FileExtensionValidator
import json


class ContentDomain(models.Model):
    """Represents a knowledge domain (e.g., Biblical texts, Buddhist teachings)"""

    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField()
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Configuration for this domain
    config = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

    def activate(self):
        """Activate this domain and deactivate others"""
        ContentDomain.objects.update(is_active=False)
        self.is_active = True
        self.save()


class ContentCategory(models.Model):
    """Categories within a domain (e.g., Old Testament, New Testament)"""

    domain = models.ForeignKey(ContentDomain, on_delete=models.CASCADE, related_name='categories')
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100)
    description = models.TextField(blank=True)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order', 'name']
        unique_together = ['domain', 'slug']

    def __str__(self):
        return f"{self.domain.name} - {self.name}"


class ContentPiece(models.Model):
    """Individual pieces of content (verses, quotes, teachings)"""

    CONTENT_TYPES = [
        ('text', 'Text'),
        ('quote', 'Quote'),
        ('teaching', 'Teaching'),
        ('story', 'Story'),
        ('meditation', 'Meditation'),
        ('prayer', 'Prayer'),
    ]

    category = models.ForeignKey(ContentCategory, on_delete=models.CASCADE, related_name='content_pieces')
    title = models.CharField(max_length=200)
    content = models.TextField()
    content_type = models.CharField(max_length=20, choices=CONTENT_TYPES, default='text')

    # Metadata
    reference = models.CharField(max_length=100, blank=True)  # e.g., "John 3:16"
    tags = models.JSONField(default=list, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    # Ordering and status
    order = models.PositiveIntegerField(default=0)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'title']

    def __str__(self):
        return self.title


class ContentFile(models.Model):
    """File-based content for bulk import"""

    domain = models.ForeignKey(ContentDomain, on_delete=models.CASCADE, related_name='content_files')
    name = models.CharField(max_length=200)
    file = models.FileField(
        upload_to='content_files/',
        validators=[FileExtensionValidator(allowed_extensions=['txt', 'json', 'csv'])]
    )
    file_type = models.CharField(max_length=10, choices=[
        ('txt', 'Text File'),
        ('json', 'JSON File'),
        ('csv', 'CSV File'),
    ])
    description = models.TextField(blank=True)
    is_processed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.domain.name} - {self.name}"


class InteractionLog(models.Model):
    """Log of user interactions for analytics and improvement"""

    INTERACTION_TYPES = [
        ('daily_guidance', 'Daily Guidance'),
        ('interpretation', 'Interpretation'),
        ('conversational', 'Conversational'),
        ('therapeutic', 'Therapeutic'),
        ('general', 'General'),
    ]

    session_id = models.CharField(max_length=100, blank=True)
    interaction_type = models.CharField(max_length=20, choices=INTERACTION_TYPES)
    user_input = models.TextField()
    ai_response = models.TextField()
    content_domain = models.CharField(max_length=100)
    context_used = models.TextField(blank=True)

    # Metadata
    response_time = models.FloatField(null=True, blank=True)  # in seconds
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.interaction_type} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
